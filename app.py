#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
雙語書籍翻譯服務 - 主应用程序
"""

import os
import json
import uuid
from datetime import datetime
from flask import Flask, request, jsonify, render_template, send_file
from flask_cors import CORS
from werkzeug.utils import secure_filename
import logging

from src.translator.gemini_translator import GeminiTranslator
from src.loader.epub_loader import EPUBLoader
from src.loader.txt_loader import TXTLoader
from src.loader.multi_format_loader import MultiFormatLoader, get_file_info
from src.generator.multi_format_generator import MultiFormatGenerator
from src.utils.file_utils import allowed_file, get_file_extension
from src.utils.auth_utils import (
    validate_email, validate_password, require_auth, optional_auth,
    get_current_user, deduct_points_for_translation, check_points_sufficient
)
from src.utils.task_queue import start_task_queue, add_translation_job
from src.models.points_transaction import PointsTransaction
from src.models.translation_job import TranslationJob
from src.models.user import User

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 配置
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key')
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['OUTPUT_FOLDER'] = 'outputs'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB

# 确保目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)
os.makedirs('data', exist_ok=True)

# 支持的文件类型 - 扩展支持更多格式
ALLOWED_EXTENSIONS = {
    'txt', 'epub', 'pdf', 'docx', 'doc', 'mobi', 'azw', 'azw3',
    'fb2', 'html', 'htm', 'rtf', 'odt', 'pdb', 'lit', 'lrf'
}

# 加载器映射
LOADER_MAP = {
    'epub': EPUBLoader,
    'txt': TXTLoader
}

@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

# 用户认证API
@app.route('/api/auth/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()

        # 验证必需字段
        if not data or 'email' not in data or 'password' not in data:
            return jsonify({'error': '邮箱和密码不能为空'}), 400

        email = data['email'].strip().lower()
        password = data['password']

        # 验证邮箱格式
        if not validate_email(email):
            return jsonify({'error': '邮箱格式不正确'}), 400

        # 验证密码强度
        password_valid, password_error = validate_password(password)
        if not password_valid:
            return jsonify({'error': password_error}), 400

        # 检查邮箱是否已存在
        existing_user = User.find_by_email(email)
        if existing_user:
            return jsonify({'error': '该邮箱已被注册'}), 400

        # 创建新用户
        user = User.create_user(email, password)

        # 生成JWT令牌
        token = user.generate_jwt_token(app.config['SECRET_KEY'])

        logger.info(f"新用户注册成功: {email}")

        return jsonify({
            'message': '注册成功',
            'user': user.to_dict(),
            'token': token
        }), 201

    except Exception as e:
        logger.error(f"用户注册失败: {str(e)}")
        return jsonify({'error': f'注册失败: {str(e)}'}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()

        # 验证必需字段
        if not data or 'email' not in data or 'password' not in data:
            return jsonify({'error': '邮箱和密码不能为空'}), 400

        email = data['email'].strip().lower()
        password = data['password']

        # 查找用户
        user = User.find_by_email(email)
        if not user:
            return jsonify({'error': '邮箱或密码错误'}), 401

        # 验证密码
        if not user.verify_password(password):
            return jsonify({'error': '邮箱或密码错误'}), 401

        # 检查用户状态
        if not user.is_active:
            return jsonify({'error': '账户已被禁用'}), 401

        # 生成JWT令牌
        token = user.generate_jwt_token(app.config['SECRET_KEY'])

        logger.info(f"用户登录成功: {email}")

        return jsonify({
            'message': '登录成功',
            'user': user.to_dict(),
            'token': token
        })

    except Exception as e:
        logger.error(f"用户登录失败: {str(e)}")
        return jsonify({'error': f'登录失败: {str(e)}'}), 500

@app.route('/api/auth/me', methods=['GET'])
@require_auth
def get_current_user_info(user):
    """获取当前用户信息"""
    return jsonify({
        'user': user.to_dict()
    })

@app.route('/api/auth/signin', methods=['POST'])
@require_auth
def daily_signin(user):
    """每日签到"""
    try:
        success = user.daily_signin()
        if success:
            return jsonify({
                'message': '签到成功，获得10积分',
                'points_balance': user.points_balance
            })
        else:
            return jsonify({
                'message': '今天已经签到过了',
                'points_balance': user.points_balance
            }), 400

    except Exception as e:
        logger.error(f"签到失败: {str(e)}")
        return jsonify({'error': f'签到失败: {str(e)}'}), 500

@app.route('/api/user/jobs', methods=['GET'])
@require_auth
def get_user_jobs(user):
    """获取用户的翻译任务历史"""
    try:
        # 获取查询参数
        limit = request.args.get('limit', 20, type=int)
        status = request.args.get('status')  # 可选的状态过滤

        # 获取所有任务
        jobs = []
        jobs_dir = 'data/jobs'

        if os.path.exists(jobs_dir):
            for filename in os.listdir(jobs_dir):
                if filename.endswith('.json'):
                    try:
                        with open(os.path.join(jobs_dir, filename), 'r', encoding='utf-8') as f:
                            job_data = json.load(f)

                        # 只返回当前用户的任务
                        if job_data.get('user_id') == user.user_id:
                            # 如果指定了状态过滤
                            if status and job_data.get('status') != status:
                                continue
                            jobs.append(job_data)
                    except Exception:
                        continue

        # 按创建时间倒序排列
        jobs.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # 限制返回数量
        jobs = jobs[:limit]

        return jsonify({
            'jobs': jobs,
            'total': len(jobs)
        })

    except Exception as e:
        logger.error(f"获取用户任务失败: {str(e)}")
        return jsonify({'error': f'获取任务失败: {str(e)}'}), 500

@app.route('/api/user/points/history', methods=['GET'])
@require_auth
def get_points_history(user):
    """获取用户的积分历史"""
    try:
        limit = request.args.get('limit', 50, type=int)

        transactions = PointsTransaction.get_user_transactions(user.user_id, limit)

        return jsonify({
            'transactions': [t.to_dict() for t in transactions],
            'current_balance': user.points_balance
        })

    except Exception as e:
        logger.error(f"获取积分历史失败: {str(e)}")
        return jsonify({'error': f'获取积分历史失败: {str(e)}'}), 500

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """文件上传接口"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if not allowed_file(file.filename, ALLOWED_EXTENSIONS):
            return jsonify({'error': '不支持的文件格式'}), 400
        
        # 生成唯一文件名
        upload_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], f"{upload_id}_{filename}")
        
        # 保存文件
        file.save(file_path)
        
        # 获取文件信息
        file_size = os.path.getsize(file_path)
        file_ext = get_file_extension(filename)
        
        logger.info(f"文件上传成功: {filename}, 大小: {file_size} bytes")
        
        return jsonify({
            'upload_id': upload_id,
            'filename': filename,
            'file_size': file_size,
            'file_type': file_ext,
            'message': '文件上传成功'
        })
        
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        return jsonify({'error': f'文件上传失败: {str(e)}'}), 500

@app.route('/api/translate', methods=['POST'])
@optional_auth
def start_translation(user):
    """开始翻译任务"""
    try:
        data = request.get_json()

        # 验证参数
        required_fields = ['upload_id', 'target_language', 'style']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必需参数: {field}'}), 400

        upload_id = data['upload_id']
        target_language = data['target_language']
        style = data['style']
        
        # 查找上传的文件
        upload_files = [f for f in os.listdir(app.config['UPLOAD_FOLDER']) 
                       if f.startswith(upload_id)]
        
        if not upload_files:
            return jsonify({'error': '找不到上传的文件'}), 404
        
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], upload_files[0])
        original_filename = upload_files[0].split('_', 1)[1]  # 移除upload_id前缀
        file_ext = get_file_extension(original_filename)
        
        # 初始化翻译器和加载器以估算成本
        multi_loader = MultiFormatLoader()

        # 检查文件格式支持
        if not multi_loader.is_supported(file_path):
            return jsonify({'error': f'不支持的文件类型: {file_ext}'}), 400

        # 预加载文件内容以计算字数
        logger.info(f"预加载文件以计算成本: {file_path}")
        try:
            chapters, original_format = multi_loader.load_file(file_path)
            logger.info(f"文件加载成功，格式: {original_format}, 章节数: {len(chapters)}")
        except Exception as e:
            logger.error(f"文件加载失败: {str(e)}")
            multi_loader.cleanup()
            return jsonify({'error': f'文件处理失败: {str(e)}'}), 400

        # 计算总字数
        total_text = ' '.join(chapters)
        word_count = len(total_text.split())
        logger.info(f"文件字数统计: {word_count} 词")

        # 限制文件大小（防止超时）
        if word_count > 10000:  # 限制为10000词
            multi_loader.cleanup()
            return jsonify({'error': f'文件太大，当前{word_count}词，最大支持10000词'}), 400

        # 计算所需积分 - 新规则：每本书固定50积分
        required_points = 50

        # 如果用户已登录，检查积分是否足够
        if user:
            sufficient, error_msg = check_points_sufficient(user, required_points)
            if not sufficient:
                multi_loader.cleanup()
                return jsonify({
                    'error': error_msg,
                    'required_points': required_points,
                    'current_points': user.points_balance
                }), 400
        else:
            # 未登录用户，提示需要登录
            multi_loader.cleanup()
            return jsonify({
                'error': '需要登录才能进行翻译',
                'required_points': required_points,
                'word_count': word_count
            }), 401

        # 创建翻译任务
        job_id = str(uuid.uuid4())
        job = TranslationJob(
            job_id=job_id,
            upload_id=upload_id,
            original_filename=original_filename,
            file_path=file_path,
            target_language=target_language,
            style=style,
            status='pending'
        )
        job.cost_points = required_points
        job.user_id = user.user_id if user else None

        # 保存任务信息
        job.save()

        # 注意：积分将在翻译成功后扣除，而不是现在扣除
        logger.info(f"任务创建成功，积分将在翻译完成后扣除: {job_id}")

        # 添加任务到后台队列进行异步处理
        add_translation_job(job_id)

        # 清理临时文件（任务队列会重新加载）
        multi_loader.cleanup()

        logger.info(f"翻译任务已提交到队列: {job_id}")

        return jsonify({
            'job_id': job_id,
            'status': 'pending',
            'message': '翻译任务已提交，正在后台处理'
        })
        
    except Exception as e:
        logger.error(f"创建翻译任务失败: {str(e)}")
        return jsonify({'error': f'创建翻译任务失败: {str(e)}'}), 500

@app.route('/api/job/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """查询任务状态"""
    try:
        job = TranslationJob.load(job_id)
        if not job:
            return jsonify({'error': '任务不存在'}), 404
        
        return jsonify(job.to_dict())
        
    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        return jsonify({'error': f'查询任务状态失败: {str(e)}'}), 500

@app.route('/api/download/<job_id>', methods=['GET'])
def download_result(job_id):
    """下载翻译结果 - 默认下载EPUB格式"""
    return download_format(job_id, 'epub')

@app.route('/api/download/<job_id>/<format_type>', methods=['GET'])
def download_format(job_id, format_type):
    """下载指定格式的翻译结果"""
    try:
        job = TranslationJob.load(job_id)
        if not job:
            return jsonify({'error': '任务不存在'}), 404

        if job.status != 'completed':
            return jsonify({'error': '任务未完成'}), 400

        # 检查是否有该格式的文件
        if format_type not in job.output_files:
            return jsonify({'error': f'不存在{format_type}格式的文件'}), 404

        file_path = job.output_files[format_type]
        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404

        # 生成下载文件名
        original_name = os.path.splitext(job.original_filename)[0]
        download_name = f"{original_name}_bilingual.{format_type}"

        return send_file(
            file_path,
            as_attachment=True,
            download_name=download_name
        )

    except Exception as e:
        logger.error(f"下载文件失败: {str(e)}")
        return jsonify({'error': f'下载文件失败: {str(e)}'}), 500

@app.route('/api/download-info/<job_id>', methods=['GET'])
def get_download_info(job_id):
    """获取下载信息"""
    try:
        job = TranslationJob.load(job_id)
        if not job:
            return jsonify({'error': '任务不存在'}), 404

        if job.status != 'completed':
            return jsonify({'error': '任务未完成'}), 400

        # 构建下载信息
        download_info = {}
        for format_type, file_path in job.output_files.items():
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                download_info[format_type] = {
                    'available': True,
                    'size': file_size,
                    'size_mb': round(file_size / (1024 * 1024), 2),
                    'download_url': f'/api/download/{job_id}/{format_type}'
                }
            else:
                download_info[format_type] = {
                    'available': False,
                    'error': '文件不存在'
                }

        return jsonify({
            'job_id': job_id,
            'original_filename': job.original_filename,
            'formats': download_info
        })

    except Exception as e:
        logger.error(f"获取下载信息失败: {str(e)}")
        return jsonify({'error': f'获取下载信息失败: {str(e)}'}), 500

if __name__ == '__main__':
    # 启动后台任务队列
    start_task_queue()
    logger.info("后台任务队列已启动")

    app.run(debug=True, host='0.0.0.0', port=5000)
