/**
 * 雙語書籍翻譯服務 - 認證模塊
 * 處理用戶登錄、註冊、簽到等功能
 */

class AuthManager {
  constructor() {
    this.currentUser = null;
    this.authModal = null;
    this.taskModal = null;
    this.init();
  }

  init() {
    this.setupModals();
    this.setupEventListeners();
  }

  // 顯示狀態消息
  showStatus(message, type) {
    const statusMessage = document.getElementById('statusMessage');
    if (statusMessage) {
      statusMessage.textContent = message;
      statusMessage.className = `status-message show ${type}`;
      statusMessage.style.display = 'block';

      // 自動隱藏成功和信息消息
      if (type === 'success' || type === 'info') {
        setTimeout(() => {
          statusMessage.style.display = 'none';
        }, 5000);
      }
    } else {
      // 如果沒有狀態消息元素，使用alert作為後備
      alert(message);
    }
  }

  // 翻譯函數
  t(key) {
    const translations = {
      'zh': {
        'login-success': '登录成功！',
        'login-failed': '登录失败',
        'register-success': '注册成功！',
        'register-failed': '注册失败',
        'password-mismatch': '两次输入的密码不一致',
        'please-login': '请先登录',
        'signin-success': '签到成功！获得10积分',
        'signin-already': '今日已签到',
        'login-title': '用户登录',
        'register-title': '用户注册',
        'loading': '加载中...',
        'no-tasks': '暂无任务',
        'no-points-history': '暂无积分记录',
        'created-at': '创建时间',
        'target-language': '目标语言',
        'translation-style': '翻译风格',
        'cost': '费用',
        'points': '积分',
        'task-pending': '等待中',
        'task-processing': '处理中',
        'task-completed': '已完成',
        'task-failed': '失败'
      },
      'en': {
        'login-success': 'Login successful!',
        'login-failed': 'Login failed',
        'register-success': 'Registration successful!',
        'register-failed': 'Registration failed',
        'password-mismatch': 'Passwords do not match',
        'please-login': 'Please login first',
        'signin-success': 'Sign-in successful! Earned 10 points',
        'signin-already': 'Already signed in today',
        'login-title': 'User Login',
        'register-title': 'User Registration',
        'loading': 'Loading...',
        'no-tasks': 'No tasks yet',
        'no-points-history': 'No points history',
        'created-at': 'Created At',
        'target-language': 'Target Language',
        'translation-style': 'Translation Style',
        'cost': 'Cost',
        'points': 'Points',
        'task-pending': 'Pending',
        'task-processing': 'Processing',
        'task-completed': 'Completed',
        'task-failed': 'Failed'
      }
    };

    const currentLang = document.documentElement.lang === 'en' ? 'en' : 'zh';
    return translations[currentLang]?.[key] || key;
  }

  setupModals() {
    this.authModal = document.getElementById('authModal');
    this.taskModal = document.getElementById('taskModal');
    
    // 點擊模態框外部關閉
    if (this.authModal) {
      this.authModal.addEventListener('click', (e) => {
        if (e.target === this.authModal) {
          this.closeAuthModal();
        }
      });
    }

    if (this.taskModal) {
      this.taskModal.addEventListener('click', (e) => {
        if (e.target === this.taskModal) {
          this.closeTaskModal();
        }
      });
    }
  }

  setupEventListeners() {
    // ESC鍵關閉模態框
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeAuthModal();
        this.closeTaskModal();
      }
    });
  }

  // 顯示認證模態框
  showAuthModal(type = 'login') {
    if (!this.authModal) return;

    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const modalTitle = document.getElementById('authModalTitle');

    if (type === 'login') {
      if (loginForm) loginForm.style.display = 'block';
      if (registerForm) registerForm.style.display = 'none';
      if (modalTitle) modalTitle.textContent = this.t('login-title');
    } else {
      if (loginForm) loginForm.style.display = 'none';
      if (registerForm) registerForm.style.display = 'block';
      if (modalTitle) modalTitle.textContent = this.t('register-title');
    }

    this.authModal.classList.add('show');
    AnimationUtils.fadeIn(this.authModal.querySelector('.md-modal__content'));
  }

  // 關閉認證模態框
  closeAuthModal() {
    if (!this.authModal) return;
    
    this.authModal.classList.remove('show');
    
    // 清空表單
    const forms = this.authModal.querySelectorAll('form');
    forms.forEach(form => form.reset());
  }

  // 切換到註冊表單
  switchToRegister() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const modalTitle = document.getElementById('authModalTitle');

    if (loginForm) loginForm.style.display = 'none';
    if (registerForm) registerForm.style.display = 'block';
    if (modalTitle) modalTitle.textContent = this.t('register-title');
  }

  // 切換到登錄表單
  switchToLogin() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const modalTitle = document.getElementById('authModalTitle');

    if (registerForm) registerForm.style.display = 'none';
    if (loginForm) loginForm.style.display = 'block';
    if (modalTitle) modalTitle.textContent = this.t('login-title');
  }

  // 處理登錄
  async handleLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('loginUsername')?.value;
    const password = document.getElementById('loginPassword')?.value;

    if (!email || !password) {
      this.showStatus('请填写完整信息', 'error');
      return;
    }

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      if (response.ok) {
        const userData = await response.json();
        localStorage.setItem('auth_token', userData.token);
        this.currentUser = userData.user;
        this.updateUserInterface(userData.user);
        this.closeAuthModal();
        this.showStatus(this.t('login-success'), 'success');
      } else {
        const error = await response.json();
        this.showStatus(`${this.t('login-failed')}: ${error.error}`, 'error');
      }
    } catch (error) {
      this.showStatus(`${this.t('login-failed')}: ${error.message}`, 'error');
    }
  }

  // 處理註冊
  async handleRegister(event) {
    event.preventDefault();
    
    const email = document.getElementById('registerEmail')?.value;
    const password = document.getElementById('registerPassword')?.value;
    const confirmPassword = document.getElementById('confirmPassword')?.value;

    if (!email || !password || !confirmPassword) {
      this.showStatus('请填写完整信息', 'error');
      return;
    }

    if (password !== confirmPassword) {
      this.showStatus(this.t('password-mismatch'), 'error');
      return;
    }

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      if (response.ok) {
        const userData = await response.json();
        localStorage.setItem('auth_token', userData.token);
        this.currentUser = userData.user;
        this.updateUserInterface(userData.user);
        this.closeAuthModal();
        this.showStatus(this.t('register-success'), 'success');
      } else {
        const error = await response.json();
        this.showStatus(`${this.t('register-failed')}: ${error.error}`, 'error');
      }
    } catch (error) {
      this.showStatus(`${this.t('register-failed')}: ${error.message}`, 'error');
    }
  }

  // 退出登錄
  async logout() {
    try {
      localStorage.removeItem('auth_token');
      this.currentUser = null;
      this.showAuthButtons();
      this.showStatus('已退出登录', 'success');
    } catch (error) {
      console.error('退出登录失败:', error);
    }
  }

  // 每日簽到
  async dailySignIn() {
    if (!this.currentUser) {
      this.showStatus(this.t('please-login'), 'error');
      return;
    }

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        document.getElementById('userPoints').textContent = result.points_balance;
        const signinBtn = document.getElementById('signinBtn');
        if (signinBtn) {
          signinBtn.disabled = true;
          signinBtn.textContent = this.t('signin-already');
        }
        this.showStatus(this.t('signin-success'), 'success');
      } else {
        const error = await response.json();
        this.showStatus(error.message, 'info');
      }
    } catch (error) {
      this.showStatus(`签到失败: ${error.message}`, 'error');
    }
  }

  // 檢查登錄狀態
  async checkLoginStatus() {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        this.showAuthButtons();
        return;
      }

      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const userData = await response.json();
        this.currentUser = userData.user;
        this.updateUserInterface(userData.user);
      } else {
        localStorage.removeItem('auth_token');
        this.showAuthButtons();
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
      localStorage.removeItem('auth_token');
      this.showAuthButtons();
    }
  }

  // 更新用戶界面
  updateUserInterface(userData) {
    const userInfo = document.getElementById('userInfo');
    const authButtons = document.getElementById('authButtons');
    const username = document.getElementById('username');
    const userPoints = document.getElementById('userPoints');
    
    if (username) username.textContent = userData.email;
    if (userPoints) userPoints.textContent = userData.points_balance;
    if (userInfo) userInfo.classList.add('show');
    if (authButtons) authButtons.style.display = 'none';

    // 檢查是否已簽到
    if (userData.last_signin && userData.last_signin === new Date().toISOString().split('T')[0]) {
      const signinBtn = document.getElementById('signinBtn');
      if (signinBtn) {
        signinBtn.disabled = true;
        signinBtn.textContent = this.t('signin-already');
      }
    }
  }

  // 顯示認證按鈕
  showAuthButtons() {
    const userInfo = document.getElementById('userInfo');
    const authButtons = document.getElementById('authButtons');
    
    if (userInfo) userInfo.classList.remove('show');
    if (authButtons) authButtons.style.display = 'flex';
  }

  // 顯示任務管理模態框
  showTaskModal() {
    if (!this.currentUser) {
      this.showStatus(this.t('please-login'), 'error');
      return;
    }

    if (this.taskModal) {
      this.taskModal.classList.add('show');
      this.loadUserTasks();
    }
  }

  // 關閉任務管理模態框
  closeTaskModal() {
    if (this.taskModal) {
      this.taskModal.classList.remove('show');
    }
  }

  // 切換標籤頁
  switchTab(tabName) {
    // 更新標籤按鈕狀態
    document.querySelectorAll('.md-button').forEach(btn => {
      if (btn.onclick && btn.onclick.toString().includes('switchTab')) {
        btn.classList.remove('active');
      }
    });
    event.target.classList.add('active');

    // 顯示對應內容
    document.querySelectorAll('.tab-content').forEach(content => {
      content.style.display = 'none';
    });
    
    const targetTab = document.getElementById(tabName + 'Tab');
    if (targetTab) {
      targetTab.style.display = 'block';
    }

    // 加載對應數據
    if (tabName === 'tasks') {
      this.loadUserTasks();
    } else if (tabName === 'points') {
      this.loadPointsHistory();
    }
  }

  // 加載用戶任務
  async loadUserTasks() {
    const taskList = document.getElementById('taskList');
    if (!taskList) return;

    taskList.innerHTML = `<div>${this.t('loading')}</div>`;

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/user/jobs?limit=20', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        this.displayTasks(data.jobs);
      } else {
        taskList.innerHTML = '<div>加载任务失败</div>';
      }
    } catch (error) {
      taskList.innerHTML = `<div>加载任务失败: ${error.message}</div>`;
    }
  }

  // 顯示任務列表
  displayTasks(tasks) {
    const taskList = document.getElementById('taskList');
    if (!taskList) return;

    if (tasks.length === 0) {
      taskList.innerHTML = `<div>${this.t('no-tasks')}</div>`;
      return;
    }

    let html = '';
    tasks.forEach(task => {
      const statusClass = task.status;
      const statusText = this.t('task-' + task.status);
      const createdAt = new Date(task.created_at).toLocaleString();

      html += `
        <div class="md-card md-card--outlined" style="margin-bottom: 16px; padding: 16px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
            <h3 class="title-medium">${task.original_filename}</h3>
            <span class="md-badge md-badge--${statusClass}">${statusText}</span>
          </div>
          <div class="body-small" style="color: var(--md-sys-color-on-surface-variant);">
            <div><strong>${this.t('created-at')}:</strong> ${createdAt}</div>
            <div><strong>${this.t('target-language')}:</strong> ${task.target_language}</div>
            <div><strong>${this.t('translation-style')}:</strong> ${task.style}</div>
            <div><strong>${this.t('cost')}:</strong> ${task.cost_points} ${this.t('points')}</div>
          </div>
          ${task.status === 'completed' ? this.generateDownloadButtons(task) : ''}
        </div>
      `;
    });

    taskList.innerHTML = html;
  }

  // 生成下載按鈕
  generateDownloadButtons(task) {
    let buttons = '<div style="margin-top: 12px; display: flex; gap: 8px; flex-wrap: wrap;">';
    if (task.output_files) {
      Object.keys(task.output_files).forEach(format => {
        buttons += `
          <a href="/api/download/${task.job_id}/${format}" 
             class="md-button md-button--tonal" 
             target="_blank">
            <span class="material-icons">download</span>
            ${format.toUpperCase()}
          </a>
        `;
      });
    }
    buttons += '</div>';
    return buttons;
  }

  // 加載積分歷史
  async loadPointsHistory() {
    const pointsHistory = document.getElementById('pointsHistory');
    if (!pointsHistory) return;

    pointsHistory.innerHTML = `<div>${this.t('loading')}</div>`;

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/user/points/history?limit=50', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        this.displayPointsHistory(data.transactions, data.current_balance);
      } else {
        pointsHistory.innerHTML = '<div>加载积分历史失败</div>';
      }
    } catch (error) {
      pointsHistory.innerHTML = `<div>加载积分历史失败: ${error.message}</div>`;
    }
  }

  // 顯示積分歷史
  displayPointsHistory(transactions, currentBalance) {
    const pointsHistory = document.getElementById('pointsHistory');
    if (!pointsHistory) return;

    if (transactions.length === 0) {
      pointsHistory.innerHTML = `<div>${this.t('no-points-history')}</div>`;
      return;
    }

    let html = `
      <div class="md-card md-card--filled" style="margin-bottom: 16px; padding: 16px;">
        <div class="title-medium">当前余额: ${currentBalance} ${this.t('points')}</div>
      </div>
    `;

    transactions.forEach(transaction => {
      const isPositive = transaction.amount > 0;
      const amountClass = isPositive ? 'success' : 'error';
      const amountText = (isPositive ? '+' : '') + transaction.amount;
      const date = new Date(transaction.created_at).toLocaleString();

      html += `
        <div class="md-card md-card--outlined" style="margin-bottom: 8px; padding: 12px;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
              <div class="body-medium">${transaction.description}</div>
              <div class="body-small" style="color: var(--md-sys-color-on-surface-variant);">${date}</div>
            </div>
            <span class="md-badge md-badge--${amountClass}">${amountText}</span>
          </div>
        </div>
      `;
    });

    pointsHistory.innerHTML = html;
  }
}

// 全局函數供HTML調用
let authManager;

// 初始化認證管理器
document.addEventListener('DOMContentLoaded', () => {
  authManager = new AuthManager();
  authManager.checkLoginStatus();
});

// 全局函數
window.showAuthModal = (type) => authManager?.showAuthModal(type);
window.closeAuthModal = () => authManager?.closeAuthModal();
window.switchToRegister = () => authManager?.switchToRegister();
window.switchToLogin = () => authManager?.switchToLogin();
window.handleLogin = (event) => authManager?.handleLogin(event);
window.handleRegister = (event) => authManager?.handleRegister(event);
window.logout = () => authManager?.logout();
window.dailySignIn = () => authManager?.dailySignIn();
window.showTaskModal = () => authManager?.showTaskModal();
window.closeTaskModal = () => authManager?.closeTaskModal();
window.switchTab = (tabName) => authManager?.switchTab(tabName);

// 導出認證管理器
window.AuthManager = AuthManager;
