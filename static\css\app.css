/* 雙語書籍翻譯服務 - 應用樣式 */

/* 導入Material Design 3基礎樣式 */
@import url('material-design-3.css');

/* 導入Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100;300;400;500;700;900&display=swap');
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

/* 全局佈局 */
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--md-sys-color-primary-container) 0%, var(--md-sys-color-tertiary-container) 100%);
  transition: background var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

.main-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

/* 頂部導航欄 */
.app-header {
  background-color: var(--md-sys-color-surface);
  box-shadow: var(--md-sys-elevation-level1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.app-title {
  color: var(--md-sys-color-on-surface);
  text-decoration: none;
  transition: color var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.app-title:hover {
  color: var(--md-sys-color-primary);
}

/* 用戶面板 */
.user-panel {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  display: none;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background-color: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-full);
  color: var(--md-sys-color-on-surface);
}

.user-info.show {
  display: flex;
}

.points-display {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 12px;
  background-color: var(--md-sys-color-tertiary-container);
  color: var(--md-sys-color-on-tertiary-container);
  border-radius: var(--md-sys-shape-corner-full);
  font-weight: 500;
}

.points-display .material-icons {
  font-size: 16px;
}

/* 語言切換器 */
.language-switcher {
  display: flex;
  gap: 8px;
}

.lang-btn {
  padding: 8px 16px;
  border: 1px solid var(--md-sys-color-outline);
  background-color: transparent;
  color: var(--md-sys-color-on-surface);
  border-radius: var(--md-sys-shape-corner-full);
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  font-size: 14px;
  font-weight: 500;
}

.lang-btn:hover {
  background-color: var(--md-sys-color-surface-container);
}

.lang-btn.active {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  border-color: var(--md-sys-color-primary);
}

/* 主要內容區域 */
.hero-section {
  text-align: center;
  margin-bottom: 48px;
  padding: 48px 0;
}

.hero-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 16px;
}

.hero-subtitle {
  color: var(--md-sys-color-on-surface-variant);
  max-width: 600px;
  margin: 0 auto;
}

/* 上傳區域 */
.upload-section {
  margin-bottom: 32px;
}

.upload-area {
  position: relative;
  border: 2px dashed var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 48px 24px;
  text-align: center;
  background-color: var(--md-sys-color-surface-container-lowest);
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  cursor: pointer;
  overflow: hidden;
}

.upload-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--md-sys-color-primary);
  opacity: 0;
  transition: opacity var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.upload-area:hover {
  border-color: var(--md-sys-color-primary);
  background-color: var(--md-sys-color-primary-container);
}

.upload-area:hover::before {
  opacity: 0.04;
}

.upload-area.dragover {
  border-color: var(--md-sys-color-primary);
  background-color: var(--md-sys-color-primary-container);
  transform: scale(1.02);
}

.upload-area.dragover::before {
  opacity: 0.08;
}

.upload-icon {
  font-size: 48px;
  color: var(--md-sys-color-primary);
  margin-bottom: 16px;
  display: block;
}

.upload-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 8px;
}

.upload-subtitle {
  color: var(--md-sys-color-on-surface-variant);
}

/* 文件信息 */
.file-info {
  display: none;
  background-color: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-medium);
  padding: 16px;
  margin: 16px 0;
  border-left: 4px solid var(--md-sys-color-primary);
}

.file-info.show {
  display: block;
}

.file-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.file-info-item:last-child {
  margin-bottom: 0;
}

.file-info-label {
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.file-info-value {
  color: var(--md-sys-color-on-surface-variant);
}

/* 表單區域 */
.form-section {
  display: grid;
  gap: 24px;
  margin-bottom: 32px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

/* 進度區域 */
.progress-section {
  display: none;
  margin: 24px 0;
}

.progress-section.show {
  display: block;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.progress-percentage {
  color: var(--md-sys-color-primary);
}

/* 狀態消息 */
.status-message {
  display: none;
  padding: 16px;
  border-radius: var(--md-sys-shape-corner-medium);
  margin: 16px 0;
  border-left: 4px solid;
}

.status-message.show {
  display: block;
}

.status-message.success {
  background-color: var(--md-sys-color-surface-container);
  border-color: #4CAF50;
  color: var(--md-sys-color-on-surface);
}

.status-message.error {
  background-color: var(--md-sys-color-error-container);
  border-color: var(--md-sys-color-error);
  color: var(--md-sys-color-on-error-container);
}

.status-message.info {
  background-color: var(--md-sys-color-surface-container);
  border-color: #2196F3;
  color: var(--md-sys-color-on-surface);
}

.status-message.warning {
  background-color: var(--md-sys-color-surface-container);
  border-color: #FF9800;
  color: var(--md-sys-color-on-surface);
}

/* 下載區域 */
.download-section {
  display: none;
  background-color: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 24px;
  margin: 24px 0;
  text-align: center;
}

.download-section.show {
  display: block;
}

.download-title {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 16px;
}

.download-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.download-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  text-decoration: none;
  border-radius: var(--md-sys-shape-corner-full);
  font-weight: 500;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  box-shadow: var(--md-sys-elevation-level1);
}

.download-btn:hover {
  box-shadow: var(--md-sys-elevation-level2);
  transform: translateY(-1px);
}

.download-btn .material-icons {
  font-size: 18px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
  
  .header-content {
    padding: 0 16px;
  }
  
  .hero-section {
    padding: 32px 0;
  }
  
  .upload-area {
    padding: 32px 16px;
  }
  
  .user-panel {
    flex-direction: column;
    gap: 8px;
  }
  
  .language-switcher {
    order: -1;
  }
  
  .download-buttons {
    flex-direction: column;
  }
}

/* 工具類 */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
