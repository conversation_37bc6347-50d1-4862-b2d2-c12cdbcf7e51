/**
 * 雙語書籍翻譯服務 - 主應用程序
 * 使用Material Design 3設計規範
 */

class TranslationApp {
  constructor() {
    this.currentUser = null;
    this.selectedFile = null;
    this.uploadId = null;
    this.currentLanguage = 'zh';
    this.translations = {};
    
    this.init();
  }

  async init() {
    await this.loadTranslations();
    this.setupEventListeners();
    this.checkLoginStatus();
    this.initializeComponents();
  }

  async loadTranslations() {
    // 多語言翻譯數據
    this.translations = {
      zh: {
        'page-title': '双语书籍翻译服务',
        'main-title': '双语书籍翻译服务',
        'main-subtitle': '将您的书籍转换为双语版本，支持多种语言和翻译风格',
        'upload-title': '拖拽文件到此处或点击选择',
        'upload-subtitle': '支持 TXT、EPUB、PDF、Word、MOBI 等16种格式，最大 100MB',
        'selected-file': '已选择文件：',
        'file-size': '文件大小：',
        'target-language': '目标语言',
        'translation-style': '翻译风格',
        'style-casual': '口语化 - 轻松自然的表达',
        'style-faithful': '严谨忠实 - 保持原文结构',
        'start-translation': '开始翻译',
        'please-select-file': '请先选择文件',
        'uploading-file': '正在上传文件...',
        'translating': '正在翻译，请稍候...',
        'translation-completed': '翻译完成！',
        'download-formats': '下载格式选择：',
        'login': '登录',
        'register': '注册',
        'logout': '退出',
        'points': '积分：',
        'daily-signin': '每日签到',
        'my-tasks': '我的任务'
      },
      en: {
        'page-title': 'Bilingual Book Translation Service',
        'main-title': 'Bilingual Book Translation Service',
        'main-subtitle': 'Convert your books to bilingual versions with support for multiple languages and translation styles',
        'upload-title': 'Drag files here or click to select',
        'upload-subtitle': 'Supports TXT, EPUB, PDF, Word, MOBI and 16 more formats, max 100MB',
        'selected-file': 'Selected file:',
        'file-size': 'File size:',
        'target-language': 'Target Language',
        'translation-style': 'Translation Style',
        'style-casual': 'Casual - Natural and relaxed expression',
        'style-faithful': 'Faithful - Maintain original structure',
        'start-translation': 'Start Translation',
        'please-select-file': 'Please select a file first',
        'uploading-file': 'Uploading file...',
        'translating': 'Translating, please wait...',
        'translation-completed': 'Translation completed!',
        'download-formats': 'Download Format Options:',
        'login': 'Login',
        'register': 'Register',
        'logout': 'Logout',
        'points': 'Points: ',
        'daily-signin': 'Daily Sign-in',
        'my-tasks': 'My Tasks'
      }
    };
  }

  setupEventListeners() {
    // 文件上傳相關
    this.setupFileUpload();
    
    // 表單提交
    this.setupFormSubmission();
    
    // 語言切換
    this.setupLanguageSwitcher();
    
    // 用戶認證
    this.setupAuthentication();
    
    // 主題變化監聽
    document.addEventListener('themechange', (e) => {
      this.onThemeChange(e.detail.theme);
    });
  }

  setupFileUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    
    if (!uploadArea || !fileInput) return;

    // 點擊上傳區域
    uploadArea.addEventListener('click', () => {
      fileInput.click();
    });

    // 拖拽事件
    uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
      uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.classList.remove('dragover');
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        this.handleFileSelect(files[0]);
      }
    });

    // 文件選擇
    fileInput.addEventListener('change', (e) => {
      if (e.target.files.length > 0) {
        this.handleFileSelect(e.target.files[0]);
      }
    });
  }

  handleFileSelect(file) {
    this.selectedFile = file;
    
    // 更新文件信息顯示
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    
    if (fileName) fileName.textContent = file.name;
    if (fileSize) fileSize.textContent = this.formatFileSize(file.size);
    if (fileInfo) {
      fileInfo.classList.add('show');
      AnimationUtils.fadeIn(fileInfo);
    }

    // 添加文件選擇動畫
    const uploadArea = document.getElementById('uploadArea');
    if (uploadArea) {
      uploadArea.style.transform = 'scale(0.98)';
      setTimeout(() => {
        uploadArea.style.transform = '';
      }, 150);
    }
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  setupFormSubmission() {
    const uploadForm = document.getElementById('uploadForm');
    if (!uploadForm) return;

    uploadForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      await this.handleTranslationSubmit();
    });
  }

  async handleTranslationSubmit() {
    if (!this.currentUser) {
      this.showStatus('请先登录', 'error');
      return;
    }

    if (!this.selectedFile) {
      this.showStatus('请先选择文件', 'error');
      return;
    }

    const translateBtn = document.getElementById('translateBtn');
    if (translateBtn) {
      translateBtn.disabled = true;
      translateBtn.textContent = this.t('uploading-file');
    }

    try {
      // 顯示進度
      this.showProgress();
      this.setProgress(10);

      // 上傳文件
      const uploadResult = await this.uploadFile();
      this.uploadId = uploadResult.upload_id;
      this.setProgress(30);

      // 開始翻譯
      const translateResult = await this.startTranslation();
      this.setProgress(50);

      // 輪詢任務狀態
      await this.pollJobStatus(translateResult.job_id);

    } catch (error) {
      this.showStatus(`错误：${error.message}`, 'error');
      this.hideProgress();
      if (translateBtn) {
        translateBtn.disabled = false;
        translateBtn.textContent = this.t('start-translation');
      }
    }
  }

  async uploadFile() {
    const formData = new FormData();
    formData.append('file', this.selectedFile);

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error('文件上传失败');
    }

    return await response.json();
  }

  async startTranslation() {
    const targetLanguage = document.getElementById('targetLanguage')?.value || '中文';
    const translationStyle = document.getElementById('translationStyle')?.value || 'casual';

    const response = await fetch('/api/translate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify({
        upload_id: this.uploadId,
        target_language: targetLanguage,
        style: translationStyle
      })
    });

    if (!response.ok) {
      throw new Error('翻译任务创建失败');
    }

    return await response.json();
  }

  async pollJobStatus(jobId) {
    const maxAttempts = 60;
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch(`/api/job/${jobId}`);
        if (!response.ok) {
          throw new Error('查询任务状态失败');
        }

        const job = await response.json();

        if (job.status === 'completed') {
          this.setProgress(100);
          this.showStatus(this.t('translation-completed'), 'success');
          this.showDownloadOptions(jobId);
          this.hideProgress();
          
          const translateBtn = document.getElementById('translateBtn');
          if (translateBtn) {
            translateBtn.disabled = false;
            translateBtn.textContent = this.t('start-translation');
          }
          return;
        }

        if (job.status === 'failed') {
          throw new Error('翻译失败');
        }

        attempts++;
        if (attempts >= maxAttempts) {
          throw new Error('翻译超时');
        }

        // 更新進度
        const progress = Math.min(50 + (attempts / maxAttempts) * 40, 90);
        this.setProgress(progress);

        setTimeout(poll, 5000);

      } catch (error) {
        throw error;
      }
    };

    await poll();
  }

  showProgress() {
    const progressSection = document.getElementById('progressContainer');
    if (progressSection) {
      progressSection.classList.add('show');
      AnimationUtils.fadeIn(progressSection);
    }
  }

  hideProgress() {
    const progressSection = document.getElementById('progressContainer');
    if (progressSection) {
      progressSection.classList.remove('show');
    }
  }

  setProgress(percent) {
    const progressBar = document.getElementById('progressBar');
    const progressPercentage = document.querySelector('.progress-percentage');
    
    if (progressBar) {
      progressBar.style.width = percent + '%';
    }
    
    if (progressPercentage) {
      progressPercentage.textContent = Math.round(percent) + '%';
    }
  }

  showStatus(message, type) {
    const statusMessage = document.getElementById('statusMessage');
    if (statusMessage) {
      statusMessage.textContent = message;
      statusMessage.className = `status-message show ${type}`;
      AnimationUtils.fadeIn(statusMessage);
      
      // 自動隱藏成功和信息消息
      if (type === 'success' || type === 'info') {
        setTimeout(() => {
          AnimationUtils.fadeOut(statusMessage);
        }, 5000);
      }
    }
  }

  async showDownloadOptions(jobId) {
    try {
      const response = await fetch(`/api/download-info/${jobId}`);
      if (!response.ok) return;

      const downloadInfo = await response.json();
      const downloadSection = document.querySelector('.download-section');
      
      if (downloadSection && downloadInfo.formats) {
        const buttonsContainer = downloadSection.querySelector('.download-buttons');
        if (buttonsContainer) {
          buttonsContainer.innerHTML = '';
          
          Object.entries(downloadInfo.formats).forEach(([format, info]) => {
            if (info.available) {
              const btn = document.createElement('a');
              btn.href = info.download_url;
              btn.className = 'download-btn md-button md-button--filled';
              btn.innerHTML = `
                <span class="material-icons">download</span>
                ${format.toUpperCase()} (${info.size_mb}MB)
              `;
              buttonsContainer.appendChild(btn);
            }
          });
        }
        
        downloadSection.classList.add('show');
        AnimationUtils.slideIn(downloadSection);
      }
    } catch (error) {
      console.error('获取下载信息失败:', error);
    }
  }

  t(key) {
    return this.translations[this.currentLanguage]?.[key] || key;
  }

  setupLanguageSwitcher() {
    const langButtons = document.querySelectorAll('.lang-btn');
    langButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        const lang = btn.dataset.lang || (btn.id === 'lang-zh' ? 'zh' : 'en');
        this.switchLanguage(lang);
      });
    });
  }

  switchLanguage(lang) {
    this.currentLanguage = lang;
    
    // 更新按鈕狀態
    document.querySelectorAll('.lang-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.getElementById(`lang-${lang}`)?.classList.add('active');
    
    // 更新頁面文本
    document.querySelectorAll('[data-i18n]').forEach(element => {
      const key = element.getAttribute('data-i18n');
      const translation = this.t(key);
      if (translation) {
        element.textContent = translation;
      }
    });
    
    // 更新placeholder文本
    document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
      const key = element.getAttribute('data-i18n-placeholder');
      const translation = this.t(key);
      if (translation) {
        element.placeholder = translation;
      }
    });
    
    // 更新頁面標題
    document.title = this.t('page-title');
    
    // 更新HTML lang屬性
    document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
  }

  async checkLoginStatus() {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        this.showAuthButtons();
        return;
      }

      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const userData = await response.json();
        this.currentUser = userData.user;
        this.updateUserInterface(userData.user);
      } else {
        localStorage.removeItem('auth_token');
        this.showAuthButtons();
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
      localStorage.removeItem('auth_token');
      this.showAuthButtons();
    }
  }

  updateUserInterface(userData) {
    const userInfo = document.getElementById('userInfo');
    const authButtons = document.getElementById('authButtons');
    const username = document.getElementById('username');
    const userPoints = document.getElementById('userPoints');
    
    if (username) username.textContent = userData.email;
    if (userPoints) userPoints.textContent = userData.points_balance;
    if (userInfo) userInfo.classList.add('show');
    if (authButtons) authButtons.style.display = 'none';
  }

  showAuthButtons() {
    const userInfo = document.getElementById('userInfo');
    const authButtons = document.getElementById('authButtons');
    
    if (userInfo) userInfo.classList.remove('show');
    if (authButtons) authButtons.style.display = 'flex';
  }

  setupAuthentication() {
    // 這裡可以添加認證相關的事件監聽器
    // 具體實現將在後續添加
  }

  initializeComponents() {
    // 初始化所有Material Design組件
    this.initializeCards();
    this.initializeButtons();
    this.initializeTextFields();
  }

  initializeCards() {
    // 為卡片添加懸停效果
    document.querySelectorAll('.md-card--elevated').forEach(card => {
      card.addEventListener('mouseenter', () => {
        card.style.transform = 'translateY(-2px)';
      });
      
      card.addEventListener('mouseleave', () => {
        card.style.transform = '';
      });
    });
  }

  initializeButtons() {
    // 按鈕已在theme.js中處理波紋效果
  }

  initializeTextFields() {
    // 處理文本字段的浮動標籤
    document.querySelectorAll('.md-text-field__input').forEach(input => {
      const updateLabel = () => {
        const label = input.nextElementSibling;
        if (label && label.classList.contains('md-text-field__label')) {
          if (input.value || input === document.activeElement) {
            label.classList.add('active');
          } else {
            label.classList.remove('active');
          }
        }
      };
      
      input.addEventListener('focus', updateLabel);
      input.addEventListener('blur', updateLabel);
      input.addEventListener('input', updateLabel);
      
      // 初始檢查
      updateLabel();
    });
  }

  onThemeChange(theme) {
    // 主題變化時的處理
    console.log('Theme changed to:', theme);
    
    // 可以在這裡添加主題變化時的特殊處理
    // 例如更新圖標、調整動畫等
  }
}

// 初始化應用
let app;

document.addEventListener('DOMContentLoaded', () => {
  app = new TranslationApp();
});

// 導出應用實例供其他模塊使用
window.TranslationApp = TranslationApp;
